import 'region_service.dart';
import 'cart_service.dart';
import 'multi_cart_service.dart';
import 'store_service.dart';

/// Global service locator for app-wide services
class AppServices {
  static final AppServices _instance = AppServices._internal();
  factory AppServices() => _instance;
  AppServices._internal();

  // Service instances
  RegionService? _regionService;
  CartService? _cartService;
  MultiCartService? _multiCartService;
  StoreService? _storeService;

  /// Get the global RegionService instance
  RegionService get regionService {
    _regionService ??= RegionService();
    return _regionService!;
  }

  /// Get the global CartService instance
  CartService get cartService {
    _cartService ??= CartService();
    return _cartService!;
  }

  /// Get the global MultiCartService instance
  MultiCartService get multiCartService {
    _multiCartService ??= MultiCartService();
    return _multiCartService!;
  }

  /// Get the global StoreService instance
  StoreService get storeService {
    _storeService ??= StoreService(regionService);
    return _storeService!;
  }

  /// Initialize all services
  Future<void> initialize() async {
    await regionService.initialize();
    await cartService.initialize();
    await multiCartService.initialize();
    await storeService.initialize();
  }

  /// Clear all services (for logout/reset)
  Future<void> clearAllServices() async {
    await regionService.clearAllData();
    await cartService.clearCart();
    await multiCartService.clearAllCarts();
    await storeService.clearSelectedStore();
    _regionService = null;
    _cartService = null;
    _multiCartService = null;
    _storeService = null;
  }
}
