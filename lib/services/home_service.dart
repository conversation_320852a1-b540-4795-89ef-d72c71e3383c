import 'package:flutter/foundation.dart';
import '../api/home/<USER>';
import '../core/models/dummy_product_model.dart';
import '../core/models/dummy_bundle_model.dart';

/// Service class for managing home page data
/// This provides a clean interface between the UI and the API layer
class HomeService extends ChangeNotifier {
  static final HomeService _instance = HomeService._internal();
  factory HomeService() => _instance;
  HomeService._internal();

  // Loading states
  bool _isLoadingPopularProducts = false;
  bool _isLoadingNewProducts = false;

  // Data
  List<ProductWithPricing> _popularProducts = [];
  List<ProductWithPricing> _newProducts = [];

  // Error states
  String? _popularProductsError;
  String? _newProductsError;

  // Getters
  bool get isLoadingPopularProducts => _isLoadingPopularProducts;
  bool get isLoadingNewProducts => _isLoadingNewProducts;
  List<ProductWithPricing> get popularProducts => _popularProducts;
  List<ProductWithPricing> get newProducts => _newProducts;
  String? get popularProductsError => _popularProductsError;
  String? get newProductsError => _newProductsError;

  /// Load popular products for the home page
  Future<void> loadPopularProducts(
      {int limit = 10, bool forceRefresh = false, int? regionId}) async {
    // Don't reload if we already have data and not forcing refresh
    if (_popularProducts.isNotEmpty && !forceRefresh) return;

    _isLoadingPopularProducts = true;
    _popularProductsError = null;
    notifyListeners();

    try {
      _popularProducts = await HomeApiService.getPopularProducts(
          limit: limit, regionId: regionId);
      _popularProductsError = null;
    } catch (e) {
      if (kDebugMode) {
        rethrow;
      }
      _popularProductsError = e.toString();
      debugPrint('Error loading popular products: $e');
    } finally {
      _isLoadingPopularProducts = false;
      notifyListeners();
    }
  }

  /// Load new products for the home page
  Future<void> loadNewProducts(
      {int limit = 10, bool forceRefresh = false, int? regionId}) async {
    // Don't reload if we already have data and not forcing refresh
    if (_newProducts.isNotEmpty && !forceRefresh) return;

    _isLoadingNewProducts = true;
    _newProductsError = null;
    notifyListeners();

    try {
      _newProducts =
          await HomeApiService.getNewProducts(limit: limit, regionId: regionId);
      _newProductsError = null;
    } catch (e) {
      _newProductsError = e.toString();
      debugPrint('Error loading new products: $e');
    } finally {
      _isLoadingNewProducts = false;
      notifyListeners();
    }
  }

  /// Load both popular and new products
  Future<void> loadHomeData({bool forceRefresh = false, int? regionId}) async {
    await Future.wait([
      loadPopularProducts(forceRefresh: forceRefresh, regionId: regionId),
      loadNewProducts(forceRefresh: forceRefresh, regionId: regionId),
    ]);
  }

  /// Search products (useful for search functionality)
  Future<List<ProductWithPricing>> searchProducts(String query,
      {int? regionId}) async {
    try {
      return await HomeApiService.searchProducts(query, regionId: regionId);
    } catch (e) {
      debugPrint('Error searching products: $e');
      rethrow;
    }
  }

  /// Get products by category
  Future<List<ProductWithPricing>> getProductsByCategory(int categoryId,
      {int? regionId}) async {
    try {
      return await HomeApiService.getProductsByCategory(categoryId,
          regionId: regionId);
    } catch (e) {
      debugPrint('Error loading category products: $e');
      rethrow;
    }
  }

  /// Get products by company
  Future<List<ProductWithPricing>> getProductsByCompany(int companyId,
      {int? regionId}) async {
    try {
      return await HomeApiService.getProductsByCompany(companyId,
          regionId: regionId);
    } catch (e) {
      debugPrint('Error loading company products: $e');
      rethrow;
    }
  }

  /// Convert ProductWithPricing to BundleModel for PopularPacks UI
  List<BundleModel> popularProductsForUI() {
    return _popularProducts.map((product) => product.toBundleModel()).toList();
  }

  /// Convert ProductWithPricing to ProductModel for NewProducts UI
  List<ProductModel> newProductsForUI() {
    return _newProducts.map((product) => product.toProductModel()).toList();
  }

  /// Clear all cached data
  void clearCache() {
    _popularProducts.clear();
    _newProducts.clear();
    _popularProductsError = null;
    _newProductsError = null;
    notifyListeners();
  }

  /// Refresh all data
  Future<void> refresh() async {
    await loadHomeData(forceRefresh: true);
  }
}
