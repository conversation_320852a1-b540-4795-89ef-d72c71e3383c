import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../api/stores_api.dart';
import '../models/store_models.dart';
import '../api/regions.dart';
import '../services/region_service.dart';

/// Service for managing store selection and automatic region setting
class StoreService extends ChangeNotifier {
  static const String _selectedStoreKey = 'selected_store';
  static const String _storesListKey = 'stores_list';
  static const String _lastUpdateKey = 'stores_last_update';

  // Cache duration - 1 hour
  static const Duration _cacheExpiry = Duration(hours: 1);

  StoreData? _selectedStore;
  List<StoreData> _allStores = [];
  bool _isLoading = false;
  String? _error;

  final RegionService _regionService;

  StoreService(this._regionService);

  // Getters
  StoreData? get selectedStore => _selectedStore;
  List<StoreData> get allStores => _allStores;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get hasSelectedStore => _selectedStore != null;

  /// Initialize the service by loading saved store and stores list
  Future<void> initialize() async {
    await _loadSavedStore();
    await _loadCachedStores();
  }

  /// Load saved store from SharedPreferences
  Future<void> _loadSavedStore() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final storeJson = prefs.getString(_selectedStoreKey);

      if (storeJson != null) {
        final storeMap = json.decode(storeJson) as Map<String, dynamic>;
        _selectedStore = StoreData.fromJson(storeMap);

        // Automatically set region based on selected store
        if (_selectedStore != null) {
          await _setRegionFromStore(_selectedStore!);
        }

        notifyListeners();
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error loading saved store: $e');
      }
    }
  }

  /// Load cached stores from SharedPreferences
  Future<void> _loadCachedStores() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final storesJson = prefs.getString(_storesListKey);
      final lastUpdateStr = prefs.getString(_lastUpdateKey);

      if (storesJson != null && lastUpdateStr != null) {
        final lastUpdate = DateTime.parse(lastUpdateStr);
        final now = DateTime.now();

        // Check if cache is still valid
        if (now.difference(lastUpdate) < _cacheExpiry) {
          final storesList = json.decode(storesJson) as List<dynamic>;
          _allStores = storesList
              .map((json) => StoreData.fromJson(json as Map<String, dynamic>))
              .toList();
          notifyListeners();
          return;
        }
      }

      // Cache is expired or doesn't exist, fetch from API
      await fetchStores(forceRefresh: true);
    } catch (e) {
      if (kDebugMode) {
        print('Error loading cached stores: $e');
      }
    }
  }

  /// Cache stores to SharedPreferences
  Future<void> _cacheStores() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final storesJson =
          json.encode(_allStores.map((store) => store.toJson()).toList());

      await prefs.setString(_storesListKey, storesJson);
      await prefs.setString(_lastUpdateKey, DateTime.now().toIso8601String());
    } catch (e) {
      if (kDebugMode) {
        print('Error caching stores: $e');
      }
    }
  }

  /// Fetch stores from API and cache them
  Future<void> fetchStores({bool forceRefresh = false}) async {
    if (_isLoading) return;

    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      // If we have cached data and not forcing refresh, return early
      if (!forceRefresh && _allStores.isNotEmpty) {
        _isLoading = false;
        notifyListeners();
        return;
      }

      final response = await StoresApiService.getUserStores();
      if (response.data != null) {
        _allStores = response.data!;
        await _cacheStores();
      } else {
        _error = response.message;
      }

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
      notifyListeners();

      if (kDebugMode) {
        print('Error fetching stores: $e');
      }
    }
  }

  /// Select a store and automatically set the region
  Future<void> selectStore(StoreData store) async {
    try {
      _selectedStore = store;

      // Save selected store
      final prefs = await SharedPreferences.getInstance();
      final storeJson = json.encode(store.toJson());
      await prefs.setString(_selectedStoreKey, storeJson);

      // Automatically set region based on store
      await _setRegionFromStore(store);

      notifyListeners();
    } catch (e) {
      if (kDebugMode) {
        print('Error saving selected store: $e');
      }
      rethrow;
    }
  }

  /// Set region based on store location
  Future<void> _setRegionFromStore(StoreData store) async {
    try {
      // Convert store region data to RegionModel
      // We'll use the city as the primary region
      final regionModel = RegionModel(
        id: store.city.id,
        name: store.city.name,
        type: store.city.type,
        code: store.city.slug, // Use slug as code
        hierarchicalName:
            '${store.country.name}, ${store.state.name}, ${store.city.name}',
      );

      await _regionService.selectRegion(regionModel);
    } catch (e) {
      if (kDebugMode) {
        print('Error setting region from store: $e');
      }
    }
  }

  /// Clear selected store
  Future<void> clearSelectedStore() async {
    try {
      _selectedStore = null;

      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_selectedStoreKey);

      // Also clear the region since it was based on store
      await _regionService.clearSelectedRegion();

      notifyListeners();
    } catch (e) {
      if (kDebugMode) {
        print('Error clearing selected store: $e');
      }
    }
  }

  /// Check if store selection is required
  bool get isStoreSelectionRequired => _selectedStore == null;

  /// Get display name for current store
  String get currentStoreDisplayName {
    return _selectedStore?.name ?? 'اختر المتجر';
  }

  /// Get current store location display
  String get currentStoreLocationDisplay {
    if (_selectedStore == null) return 'لم يتم تحديد الموقع';
    return '${_selectedStore!.city.name}, ${_selectedStore!.state.name}';
  }

  /// Refresh stores and clear cache
  Future<void> refreshStores() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_storesListKey);
    await prefs.remove(_lastUpdateKey);

    _allStores.clear();
    await fetchStores(forceRefresh: true);
  }

  /// Search stores by name
  List<StoreData> searchStores(String query) {
    if (query.isEmpty) return _allStores;

    final lowercaseQuery = query.toLowerCase();
    return _allStores.where((store) {
      return store.name.toLowerCase().contains(lowercaseQuery) ||
          store.address.toLowerCase().contains(lowercaseQuery) ||
          store.city.name.toLowerCase().contains(lowercaseQuery);
    }).toList();
  }
}
