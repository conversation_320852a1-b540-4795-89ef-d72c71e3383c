import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/cart_models.dart';
import '../api/wholesaler_api.dart';

/// Service for managing multiple shopping carts (one per wholesaler)
class MultiCartService extends ChangeNotifier {
  static const String _cartsKey = 'multi_shopping_carts';
  static const String _activeWholesalerKey = 'active_wholesaler_id';

  Map<int, Cart> _carts = {}; // wholesaler_id -> Cart
  int? _activeWholesalerId;
  bool _isLoading = false;
  String? _error;

  // Getters
  Map<int, Cart> get allCarts => Map.unmodifiable(_carts);
  Cart? get activeCart =>
      _activeWholesalerId != null ? _carts[_activeWholesalerId] : null;
  int? get activeWholesalerId => _activeWholesalerId;
  bool get hasActiveCarts => _carts.isNotEmpty;
  bool get hasActiveCart => activeCart != null && activeCart!.isNotEmpty;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // Active cart summary getters
  int get activeCartItemCount => activeCart?.itemCount ?? 0;
  int get activeCartTotalQuantity => activeCart?.totalQuantity ?? 0;
  double get activeCartTotalPrice => activeCart?.totalPrice ?? 0.0;
  String? get activeCartWholesalerTitle => activeCart?.wholesalerTitle;

  // All carts summary getters
  int get totalCartsCount => _carts.length;
  int get totalItemsAcrossAllCarts =>
      _carts.values.fold(0, (sum, cart) => sum + cart.itemCount);
  double get totalPriceAcrossAllCarts =>
      _carts.values.fold(0.0, (sum, cart) => sum + cart.totalPrice);

  /// Initialize the service by loading saved carts
  Future<void> initialize() async {
    await _loadCartsFromStorage();
    await _loadActiveWholesaler();
  }

  /// Load carts from SharedPreferences
  Future<void> _loadCartsFromStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cartsJson = prefs.getString(_cartsKey);

      if (cartsJson != null) {
        final cartsMap = json.decode(cartsJson) as Map<String, dynamic>;
        _carts = cartsMap.map((key, value) => MapEntry(
              int.parse(key),
              Cart.fromJson(value as Map<String, dynamic>),
            ));
        notifyListeners();
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error loading carts from storage: $e');
      }
    }
  }

  /// Load active wholesaler from SharedPreferences
  Future<void> _loadActiveWholesaler() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _activeWholesalerId = prefs.getInt(_activeWholesalerKey);
      notifyListeners();
    } catch (e) {
      if (kDebugMode) {
        print('Error loading active wholesaler: $e');
      }
    }
  }

  /// Save carts to SharedPreferences
  Future<void> _saveCartsToStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cartsMap =
          _carts.map((key, value) => MapEntry(key.toString(), value.toJson()));
      final cartsJson = json.encode(cartsMap);
      await prefs.setString(_cartsKey, cartsJson);
    } catch (e) {
      if (kDebugMode) {
        print('Error saving carts to storage: $e');
      }
    }
  }

  /// Save active wholesaler to SharedPreferences
  Future<void> _saveActiveWholesaler() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      if (_activeWholesalerId != null) {
        await prefs.setInt(_activeWholesalerKey, _activeWholesalerId!);
      } else {
        await prefs.remove(_activeWholesalerKey);
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error saving active wholesaler: $e');
      }
    }
  }

  /// Set the active wholesaler (switches context to that wholesaler's cart)
  Future<void> setActiveWholesaler(int wholesalerId) async {
    _activeWholesalerId = wholesalerId;
    await _saveActiveWholesaler();
    notifyListeners();
  }

  /// Add item to a specific wholesaler's cart
  Future<CartOperationResponse> addToCart({
    required int wholesalerId,
    required int wholesalerItemId,
    required WholesalerInfo wholesalerInfo,
    int quantity = 1,
  }) async {
    try {
      _setLoading(true);
      _clearError();

      // Get item details from API
      final itemResponse =
          await WholesalerApiService.getWholesalerItemById(wholesalerItemId);

      // Validate quantity constraints
      if (quantity < itemResponse.minimumOrderQuantity) {
        return CartOperationResponse(
          result: CartOperationResult.quantityBelowMinimum,
          message: 'الحد الأدنى للطلب هو ${itemResponse.minimumOrderQuantity}',
        );
      }

      if (itemResponse.maximumOrderQuantity != null &&
          quantity > itemResponse.maximumOrderQuantity!) {
        return CartOperationResponse(
          result: CartOperationResult.quantityAboveMaximum,
          message: 'الحد الأقصى للطلب هو ${itemResponse.maximumOrderQuantity}',
        );
      }

      // Check inventory
      if (quantity > itemResponse.inventoryCount) {
        return CartOperationResponse(
          result: CartOperationResult.insufficientInventory,
          message: 'الكمية المتاحة: ${itemResponse.inventoryCount}',
        );
      }

      // Create cart item
      final cartItem = CartItem(
        itemId: itemResponse.productId,
        productName: itemResponse.productName,
        price: itemResponse.price,
        quantity: quantity,
        imageUrl: itemResponse.imageUrl,
        unit: itemResponse.unit,
        unitCount: itemResponse.unitCount,
        wholesalerItemId: wholesalerItemId,
        minimumOrderQuantity: itemResponse.minimumOrderQuantity,
        maximumOrderQuantity: itemResponse.maximumOrderQuantity,
        addedAt: DateTime.now(),
      );

      // Get or create cart for this wholesaler
      Cart cart;
      if (_carts.containsKey(wholesalerId)) {
        cart = _carts[wholesalerId]!;

        // Check if item already exists
        final existingItemIndex = cart.items
            .indexWhere((item) => item.wholesalerItemId == wholesalerItemId);

        List<CartItem> updatedItems;
        if (existingItemIndex != -1) {
          // Update existing item quantity
          updatedItems = List.from(cart.items);
          updatedItems[existingItemIndex] = updatedItems[existingItemIndex]
              .copyWith(
                  quantity:
                      updatedItems[existingItemIndex].quantity + quantity);
        } else {
          // Add new item
          updatedItems = [...cart.items, cartItem];
        }

        cart = cart.copyWith(items: updatedItems);
      } else {
        // Create new cart for this wholesaler
        cart = Cart(
          wholesalerId: wholesalerId,
          wholesalerTitle: wholesalerInfo.title,
          wholesalerLogo: wholesalerInfo.logoUrl,
          items: [cartItem],
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
      }

      _carts[wholesalerId] = cart;

      await _saveCartsToStorage();
      notifyListeners();

      return const CartOperationResponse(result: CartOperationResult.success);
    } catch (e) {
      _setError('حدث خطأ أثناء إضافة المنتج إلى السلة: ${e.toString()}');
      return const CartOperationResponse(
        result: CartOperationResult.error,
        message: 'حدث خطأ أثناء إضافة المنتج إلى السلة.',
      );
    } finally {
      _setLoading(false);
    }
  }

  /// Remove item from a specific wholesaler's cart
  Future<CartOperationResponse> removeFromCart(
      int wholesalerId, int wholesalerItemId) async {
    if (!_carts.containsKey(wholesalerId)) {
      return const CartOperationResponse(
        result: CartOperationResult.itemNotFound,
        message: 'السلة غير موجودة.',
      );
    }

    try {
      _setLoading(true);
      _clearError();

      final cart = _carts[wholesalerId]!;
      final updatedItems = cart.items
          .where((item) => item.wholesalerItemId != wholesalerItemId)
          .toList();

      if (updatedItems.isEmpty) {
        // Remove entire cart if no items left
        _carts.remove(wholesalerId);
        if (_activeWholesalerId == wholesalerId) {
          _activeWholesalerId = null;
        }
      } else {
        _carts[wholesalerId] = cart.copyWith(items: updatedItems);
      }

      await _saveCartsToStorage();
      await _saveActiveWholesaler();
      notifyListeners();

      return const CartOperationResponse(result: CartOperationResult.success);
    } catch (e) {
      _setError('حدث خطأ أثناء حذف المنتج من السلة: ${e.toString()}');
      return const CartOperationResponse(
        result: CartOperationResult.error,
        message: 'حدث خطأ أثناء حذف المنتج من السلة.',
      );
    } finally {
      _setLoading(false);
    }
  }

  /// Update item quantity in a specific wholesaler's cart
  Future<CartOperationResponse> updateItemQuantity(
    int wholesalerId,
    int wholesalerItemId,
    int newQuantity,
  ) async {
    if (!_carts.containsKey(wholesalerId)) {
      return const CartOperationResponse(
        result: CartOperationResult.itemNotFound,
        message: 'السلة غير موجودة.',
      );
    }

    if (newQuantity <= 0) {
      return removeFromCart(wholesalerId, wholesalerItemId);
    }

    try {
      _setLoading(true);
      _clearError();

      final cart = _carts[wholesalerId]!;
      final itemIndex = cart.items
          .indexWhere((item) => item.wholesalerItemId == wholesalerItemId);

      if (itemIndex == -1) {
        return const CartOperationResponse(
          result: CartOperationResult.itemNotFound,
          message: 'المنتج غير موجود في السلة.',
        );
      }

      final item = cart.items[itemIndex];

      // Validate quantity constraints
      if (newQuantity < item.minimumOrderQuantity) {
        return CartOperationResponse(
          result: CartOperationResult.quantityBelowMinimum,
          message: 'الحد الأدنى للطلب هو ${item.minimumOrderQuantity}',
        );
      }

      if (item.maximumOrderQuantity != null &&
          newQuantity > item.maximumOrderQuantity!) {
        return CartOperationResponse(
          result: CartOperationResult.quantityAboveMaximum,
          message: 'الحد الأقصى للطلب هو ${item.maximumOrderQuantity}',
        );
      }

      final updatedItems = List<CartItem>.from(cart.items);
      updatedItems[itemIndex] = item.copyWith(quantity: newQuantity);

      _carts[wholesalerId] = cart.copyWith(items: updatedItems);

      await _saveCartsToStorage();
      notifyListeners();

      return const CartOperationResponse(result: CartOperationResult.success);
    } catch (e) {
      _setError('حدث خطأ أثناء تحديث كمية المنتج: ${e.toString()}');
      return const CartOperationResponse(
        result: CartOperationResult.error,
        message: 'حدث خطأ أثناء تحديث كمية المنتج.',
      );
    } finally {
      _setLoading(false);
    }
  }

  /// Clear a specific wholesaler's cart
  Future<void> clearCart(int wholesalerId) async {
    try {
      _carts.remove(wholesalerId);
      if (_activeWholesalerId == wholesalerId) {
        _activeWholesalerId = null;
      }
      await _saveCartsToStorage();
      await _saveActiveWholesaler();
      notifyListeners();
    } catch (e) {
      _setError('حدث خطأ أثناء إفراغ السلة: ${e.toString()}');
    }
  }

  /// Clear all carts
  Future<void> clearAllCarts() async {
    try {
      _carts.clear();
      _activeWholesalerId = null;
      await _saveCartsToStorage();
      await _saveActiveWholesaler();
      notifyListeners();
    } catch (e) {
      _setError('حدث خطأ أثناء إفراغ جميع السلات: ${e.toString()}');
    }
  }

  /// Get cart for a specific wholesaler
  Cart? getCartForWholesaler(int wholesalerId) {
    return _carts[wholesalerId];
  }

  /// Check if product is in any cart
  bool isInAnyCart(int wholesalerItemId) {
    return _carts.values.any((cart) =>
        cart.items.any((item) => item.wholesalerItemId == wholesalerItemId));
  }

  /// Check if product is in a specific wholesaler's cart
  bool isInCart(int wholesalerId, int wholesalerItemId) {
    final cart = _carts[wholesalerId];
    if (cart == null) return false;
    return cart.items.any((item) => item.wholesalerItemId == wholesalerItemId);
  }

  /// Get item quantity in a specific wholesaler's cart
  int getItemQuantity(int wholesalerId, int wholesalerItemId) {
    final cart = _carts[wholesalerId];
    if (cart == null) return 0;

    try {
      final item = cart.items
          .firstWhere((item) => item.wholesalerItemId == wholesalerItemId);
      return item.quantity;
    } catch (e) {
      return 0;
    }
  }

  /// Get cart item from a specific wholesaler's cart
  CartItem? getCartItem(int wholesalerId, int wholesalerItemId) {
    final cart = _carts[wholesalerId];
    if (cart == null) return null;

    try {
      return cart.items
          .firstWhere((item) => item.wholesalerItemId == wholesalerItemId);
    } catch (e) {
      return null;
    }
  }

  /// Private methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }
}
