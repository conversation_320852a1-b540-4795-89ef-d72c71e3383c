import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import '../api.dart';
import 'models.dart';

class HomeApiService {
  static const String _baseEndpoint = '/api/v2/home';

  /// Get products from the backend with filtering and pagination
  ///
  /// Example usage:
  /// ```dart
  /// // Get popular products (first 10)
  /// final popular = await HomeApiService.getProducts(ProductFilters(pageSize: 10));
  ///
  /// // Get new products (second page)
  /// final newProducts = await HomeApiService.getProducts(ProductFilters(page: 2, pageSize: 10));
  ///
  /// // Search products
  /// final searchResults = await HomeApiService.getProducts(ProductFilters(search: 'apple'));
  /// ```
  static Future<PaginatedProductResponse> getProducts(
      [ProductFilters? filters]) async {
    try {
      final queryParams =
          filters?.toQueryParams() ?? ProductFilters().toQueryParams();

      // Build query string
      final queryString = queryParams.entries
          .map((entry) =>
              '${entry.key}=${Uri.encodeComponent(entry.value.toString())}')
          .join('&');

      final url = '$_baseEndpoint/products?$queryString';

      final response = await HttpService.instance.get(url);

      return PaginatedProductResponse.fromJson(response);
    } on DioException catch (e) {
      throw _handleApiError(e);
    } catch (e) {
      if (kDebugMode) {
        rethrow;
      }
      throw HomeApiException('Unexpected error occurred: ${e.toString()}');
    }
  }

  /// Get popular products specifically (typically first page with limited results)
  static Future<List<ProductWithPricing>> getPopularProducts(
      {int limit = 10, int? regionId}) async {
    try {
      final filters =
          ProductFilters(page: 1, pageSize: limit, regionId: regionId);
      final response = await getProducts(filters);
      return response.products;
    } catch (e) {
      if (kDebugMode) {
        rethrow;
      }
      throw HomeApiException(
          'Failed to load popular products: ${e.toString()}');
    }
  }

  /// Get new products specifically (typically sorted by creation date)
  static Future<List<ProductWithPricing>> getNewProducts(
      {int limit = 10, int? regionId}) async {
    try {
      // For now, we'll get the second page of products as "new items"
      // In the future, this could be enhanced with a specific "new products" endpoint
      final filters =
          ProductFilters(page: 1, pageSize: limit, regionId: regionId);
      final response = await getProducts(filters);
      return response.products;
    } catch (e) {
      if (kDebugMode) {
        rethrow;
      }
      throw HomeApiException('Failed to load new products: ${e.toString()}');
    }
  }

  /// Search products by query
  static Future<List<ProductWithPricing>> searchProducts(String query,
      {int limit = 20, int? regionId}) async {
    try {
      final filters =
          ProductFilters(search: query, pageSize: limit, regionId: regionId);
      final response = await getProducts(filters);
      return response.products;
    } catch (e) {
      if (kDebugMode) {
        rethrow;
      }
      throw HomeApiException('Failed to search products: ${e.toString()}');
    }
  }

  /// Get products by category
  static Future<List<ProductWithPricing>> getProductsByCategory(int categoryId,
      {int limit = 20, int? regionId}) async {
    try {
      final filters = ProductFilters(
          categoryId: categoryId, pageSize: limit, regionId: regionId);
      final response = await getProducts(filters);
      return response.products;
    } catch (e) {
      throw HomeApiException(
          'Failed to load category products: ${e.toString()}');
    }
  }

  /// Get products by company
  static Future<List<ProductWithPricing>> getProductsByCompany(int companyId,
      {int limit = 20, int? regionId}) async {
    try {
      final filters = ProductFilters(
          companyId: companyId, pageSize: limit, regionId: regionId);
      final response = await getProducts(filters);
      return response.products;
    } catch (e) {
      throw HomeApiException(
          'Failed to load company products: ${e.toString()}');
    }
  }

  /// Handle API errors and convert them to user-friendly messages
  static HomeApiException _handleApiError(DioException error) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return HomeApiException(
            'Connection timeout. Please check your internet connection.');

      case DioExceptionType.badResponse:
        final statusCode = error.response?.statusCode;
        final message =
            error.response?.data?['message'] ?? 'Server error occurred';

        switch (statusCode) {
          case 400:
            return HomeApiException('Invalid request: $message');
          case 401:
            return HomeApiException(
                'Authentication required. Please log in again.');
          case 403:
            return HomeApiException('Access denied: $message');
          case 404:
            return HomeApiException('Products not found');
          case 500:
            return HomeApiException('Server error. Please try again later.');
          default:
            return HomeApiException('Error $statusCode: $message');
        }

      case DioExceptionType.cancel:
        return HomeApiException('Request was cancelled');

      case DioExceptionType.connectionError:
        return HomeApiException(
            'No internet connection. Please check your network.');

      default:
        return HomeApiException('Network error: ${error.message}');
    }
  }
}

/// Custom exception for home API errors
class HomeApiException implements Exception {
  final String message;

  HomeApiException(this.message);

  @override
  String toString() => 'HomeApiException: $message';
}
