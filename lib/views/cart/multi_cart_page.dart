import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../core/constants/app_colors.dart';
import '../../core/constants/app_defaults.dart';
import '../../core/components/app_back_button.dart';
import '../../services/multi_cart_service.dart';
import '../../models/cart_models.dart';
import '../../utils/navigation.dart' as nav;
import 'cart_page.dart';
import '../home/<USER>';

class MultiCartPage extends StatefulWidget {
  final bool isHomePage;

  const MultiCartPage({
    super.key,
    this.isHomePage = false,
  });

  @override
  State<MultiCartPage> createState() => _MultiCartPageState();
}

class _MultiCartPageState extends State<MultiCartPage> {
  late MultiCartService _multiCartService;

  @override
  void initState() {
    super.initState();
    _multiCartService = context.read<MultiCartService>();
  }

  void _navigateToCart(int wholesalerId) {
    // Set active wholesaler and navigate to single cart view
    _multiCartService.setActiveWholesaler(wholesalerId);
    nav.Router.push(
      context,
      const CartPage(isHomePage: false),
    );
  }

  void _navigateToWholesaler(int wholesalerId) {
    // Navigate to wholesaler products without setting as active
    nav.Router.push(
      context,
      WholesalerProductsPage(
        wholesalerId: wholesalerId,
        isHomePage: false,
      ),
    );
  }

  Future<void> _clearCart(int wholesalerId) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: const Text('هل تريد حذف جميع المنتجات من هذه السلة؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      await _multiCartService.clearCart(wholesalerId);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.cardColor,
      appBar: widget.isHomePage
          ? null
          : AppBar(
              leading: const AppBackButton(),
              title: const Text('السلات'),
              backgroundColor: AppColors.cardColor,
              elevation: 0,
            ),
      body: Consumer<MultiCartService>(
        builder: (context, multiCartService, child) {
          if (!multiCartService.hasActiveCarts) {
            return _buildEmptyState();
          }

          return Column(
            children: [
              // Summary header
              Container(
                margin: const EdgeInsets.all(AppDefaults.margin),
                padding: const EdgeInsets.all(AppDefaults.padding),
                decoration: BoxDecoration(
                  color: AppColors.scaffoldBackground,
                  borderRadius: AppDefaults.borderRadius,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'إجمالي السلات',
                          style:
                              Theme.of(context).textTheme.titleMedium?.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ),
                        ),
                        Text(
                          '${multiCartService.totalCartsCount} تاجر',
                          style:
                              Theme.of(context).textTheme.bodyMedium?.copyWith(
                                    color: Colors.grey[600],
                                  ),
                        ),
                      ],
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          '${multiCartService.totalPriceAcrossAllCarts.toStringAsFixed(2)} ج.م',
                          style:
                              Theme.of(context).textTheme.titleMedium?.copyWith(
                                    fontWeight: FontWeight.bold,
                                    color: AppColors.primary,
                                  ),
                        ),
                        Text(
                          '${multiCartService.totalItemsAcrossAllCarts} منتج',
                          style:
                              Theme.of(context).textTheme.bodyMedium?.copyWith(
                                    color: Colors.grey[600],
                                  ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              // Carts list
              Expanded(
                child: ListView.builder(
                  padding: const EdgeInsets.symmetric(
                      horizontal: AppDefaults.margin),
                  itemCount: multiCartService.allCarts.length,
                  itemBuilder: (context, index) {
                    final entry =
                        multiCartService.allCarts.entries.elementAt(index);
                    final wholesalerId = entry.key;
                    final cart = entry.value;

                    return _CartSummaryCard(
                      cart: cart,
                      isActive:
                          multiCartService.activeWholesalerId == wholesalerId,
                      onTap: () => _navigateToCart(wholesalerId),
                      onWholesalerTap: () =>
                          _navigateToWholesaler(wholesalerId),
                      onClear: () => _clearCart(wholesalerId),
                    );
                  },
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.shopping_cart_outlined,
            size: 80,
            color: Colors.grey[300],
          ),
          const SizedBox(height: 24),
          Text(
            'لا توجد منتجات في السلة',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  color: Colors.grey[600],
                ),
          ),
          const SizedBox(height: 12),
          Text(
            'ابدأ بإضافة منتجات من التجار المختلفين',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: Colors.grey[500],
                ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

class _CartSummaryCard extends StatelessWidget {
  final Cart cart;
  final bool isActive;
  final VoidCallback onTap;
  final VoidCallback onWholesalerTap;
  final VoidCallback onClear;

  const _CartSummaryCard({
    required this.cart,
    required this.isActive,
    required this.onTap,
    required this.onWholesalerTap,
    required this.onClear,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppDefaults.margin),
      decoration: BoxDecoration(
        color: AppColors.scaffoldBackground,
        borderRadius: AppDefaults.borderRadius,
        border:
            isActive ? Border.all(color: AppColors.primary, width: 2) : null,
      ),
      child: Column(
        children: [
          // Header with wholesaler info
          Container(
            padding: const EdgeInsets.all(AppDefaults.padding),
            decoration: BoxDecoration(
              color: isActive
                  ? AppColors.primary.withValues(alpha: 0.1)
                  : Colors.grey[50],
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(AppDefaults.radius),
                topRight: Radius.circular(AppDefaults.radius),
              ),
            ),
            child: Row(
              children: [
                // Wholesaler logo/icon
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: isActive ? AppColors.primary : Colors.grey[300],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.store,
                    color: isActive ? Colors.white : Colors.grey[600],
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),

                // Wholesaler info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        cart.wholesalerTitle,
                        style: Theme.of(context)
                            .textTheme
                            .titleMedium
                            ?.copyWith(
                              fontWeight: FontWeight.bold,
                              color:
                                  isActive ? AppColors.primary : Colors.black,
                            ),
                      ),
                      Text(
                        '${cart.itemCount} منتج',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Colors.grey[600],
                            ),
                      ),
                    ],
                  ),
                ),

                // Actions
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    IconButton(
                      onPressed: onWholesalerTap,
                      icon: const Icon(Icons.store_outlined),
                      tooltip: 'زيارة التاجر',
                    ),
                    IconButton(
                      onPressed: onClear,
                      icon: const Icon(Icons.delete_outline),
                      iconSize: 20,
                      tooltip: 'حذف السلة',
                      color: Colors.red,
                    ),
                  ],
                ),
              ],
            ),
          ),

          // Cart summary
          InkWell(
            onTap: onTap,
            borderRadius: const BorderRadius.only(
              bottomLeft: Radius.circular(AppDefaults.radius),
              bottomRight: Radius.circular(AppDefaults.radius),
            ),
            child: Container(
              padding: const EdgeInsets.all(AppDefaults.padding),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'إجمالي الكمية',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Colors.grey[600],
                            ),
                      ),
                      Text(
                        '${cart.totalQuantity} قطعة',
                        style: Theme.of(context).textTheme.titleSmall?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                      ),
                    ],
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        'إجمالي السعر',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Colors.grey[600],
                            ),
                      ),
                      Text(
                        '${cart.totalPrice.toStringAsFixed(2)} ج.م',
                        style: Theme.of(context).textTheme.titleSmall?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: AppColors.primary,
                            ),
                      ),
                    ],
                  ),
                  Icon(
                    Icons.arrow_forward_ios,
                    size: 16,
                    color: Colors.grey[400],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
