import 'package:flutter/material.dart';
import 'package:grocery/views/entrypoint/entrypoint_ui.dart';
import 'package:provider/provider.dart';

import '../../core/components/app_back_button.dart';
import '../../core/constants/app_defaults.dart';
import '../../services/app_services.dart';
import '../../services/cart_service.dart';
import '../../services/multi_cart_service.dart';
import '../../models/cart_models.dart';
import '../../api/wholesaler_api.dart';
import '../../utils/navigation.dart' as nav;
import '../../views/cart/checkout_page.dart';
import 'components/wholesaler_cart_item_tile.dart';
import 'components/cart_wholesaler_header.dart';
import 'components/cart_min_charge_card.dart';
import 'components/cart_totals_card.dart';

class CartPage extends StatefulWidget {
  const CartPage({
    super.key,
    this.isHomePage = false,
  });

  final bool isHomePage;

  @override
  State<CartPage> createState() => _CartPageState();
}

class _CartPageState extends State<CartPage> {
  WholesalerMinChargeResponse? _minChargeInfo;
  bool _isLoadingMinCharge = false;
  String? _minChargeError;

  late final MultiCartService _multiCartService;
  late final CartService _cartService;

  @override
  void initState() {
    super.initState();
    _multiCartService = AppServices().multiCartService;
    _cartService = AppServices().cartService;
    _loadMinChargeInfo();
  }

  Future<void> _loadMinChargeInfo() async {
    final regionService = AppServices().regionService;

    if (!_hasCart ||
        regionService.selectedRegion == null ||
        _currentWholesalerId == null) {
      return;
    }

    setState(() {
      _isLoadingMinCharge = true;
      _minChargeError = null;
    });

    try {
      final minCharge = await WholesalerApiService.getWholesalerMinCharge(
        _currentWholesalerId!,
        regionService.selectedRegion!.id,
      );

      if (mounted) {
        setState(() {
          _minChargeInfo = minCharge;
          _isLoadingMinCharge = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _minChargeError = 'حدث خطأ في تحميل الحد الأدنى للطلب';
          _isLoadingMinCharge = false;
        });
      }
    }
  }

  /// Get the current cart data - prioritize multi-cart over single cart
  Cart? get _currentCart {
    if (_multiCartService.activeWholesalerId != null) {
      return _multiCartService.activeCart;
    }
    return _cartService.cart;
  }

  /// Get the current wholesaler ID
  int? get _currentWholesalerId {
    if (_multiCartService.activeWholesalerId != null) {
      return _multiCartService.activeWholesalerId;
    }
    return _cartService.currentWholesalerId;
  }

  /// Check if we have any cart
  bool get _hasCart {
    return _currentCart != null && _currentCart!.isNotEmpty;
  }

  /// Get total price from current cart
  double get _totalPrice {
    return _currentCart?.totalPrice ?? 0.0;
  }

  /// Get item count from current cart
  int get _itemCount {
    return _currentCart?.itemCount ?? 0;
  }

  /// Get total quantity from current cart
  int get _totalQuantity {
    return _currentCart?.totalQuantity ?? 0;
  }

  /// Update item quantity in the appropriate cart service
  Future<void> _updateItemQuantity(
      int wholesalerItemId, int newQuantity) async {
    if (_multiCartService.activeWholesalerId != null) {
      await _multiCartService.updateItemQuantity(
        _multiCartService.activeWholesalerId!,
        wholesalerItemId,
        newQuantity,
      );
    } else {
      await _cartService.updateQuantity(wholesalerItemId, newQuantity);
    }
  }

  /// Remove item from the appropriate cart service
  Future<void> _removeItem(int wholesalerItemId) async {
    if (_multiCartService.activeWholesalerId != null) {
      await _multiCartService.removeFromCart(
        _multiCartService.activeWholesalerId!,
        wholesalerItemId,
      );
    } else {
      await _cartService.removeFromCart(wholesalerItemId);
    }

    // Refresh min charge info when cart becomes empty
    if (!_hasCart) {
      setState(() {
        _minChargeInfo = null;
      });
    }
  }

  bool get _canProceedToCheckout {
    if (!_hasCart || _minChargeInfo == null) return false;

    return _totalPrice >= _minChargeInfo!.minCharge &&
        _itemCount >= _minChargeInfo!.minItems;
  }

  void _showMinChargeError() {
    if (_minChargeInfo == null) return;

    String message = '';
    if (_totalPrice < _minChargeInfo!.minCharge) {
      final needed = _minChargeInfo!.minCharge - _totalPrice;
      message +=
          'تحتاج إلى ${needed.toStringAsFixed(2)} جنيه أخرى للوصول للحد الأدنى';
    }
    if (_itemCount < _minChargeInfo!.minItems) {
      final needed = _minChargeInfo!.minItems - _itemCount;
      if (message.isNotEmpty) message += '\n';
      message += 'تحتاج إلى $needed منتج أخرى للوصول للحد الأدنى';
    }

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.orange,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: widget.isHomePage
          ? null
          : AppBar(
              leading: const AppBackButton(),
              title: const Text(
                'سلة التسوق',
                style: TextStyle(
                  fontFamily: 'Gilroy',
                  fontWeight: FontWeight.bold,
                ),
              ),
              centerTitle: true,
            ),
      body: Consumer2<CartService, MultiCartService>(
        builder: (context, cartService, multiCartService, child) {
          // Check if either service is loading
          if (cartService.isLoading || multiCartService.isLoading) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          if (!_hasCart) {
            return _buildEmptyCart();
          }

          return SafeArea(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  // Wholesaler Header
                  CartWholesalerHeader(cart: _currentCart!),

                  const SizedBox(height: AppDefaults.padding),

                  // Cart Items
                  ListView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: _currentCart!.items.length,
                    itemBuilder: (context, index) {
                      final item = _currentCart!.items[index];
                      return WholesalerCartItemTile(
                        cartItem: item,
                        onQuantityChanged: (newQuantity) async {
                          await _updateItemQuantity(
                            item.wholesalerItemId,
                            newQuantity,
                          );
                        },
                        onRemove: () async {
                          await _removeItem(item.wholesalerItemId);
                        },
                      );
                    },
                  ),

                  const SizedBox(height: AppDefaults.padding),

                  // Minimum Charge Information
                  if (_minChargeInfo != null)
                    CartMinChargeCard(
                      minChargeInfo: _minChargeInfo!,
                      currentTotal: _totalPrice,
                      currentItems: _itemCount, // Number of different products
                    ),

                  if (_isLoadingMinCharge)
                    const Padding(
                      padding: EdgeInsets.all(AppDefaults.padding),
                      child: Center(child: CircularProgressIndicator()),
                    ),

                  if (_minChargeError != null)
                    Padding(
                      padding: const EdgeInsets.all(AppDefaults.padding),
                      child: Container(
                        padding: const EdgeInsets.all(AppDefaults.padding),
                        decoration: BoxDecoration(
                          color: Colors.red.shade50,
                          borderRadius:
                              BorderRadius.circular(AppDefaults.radius),
                          border: Border.all(color: Colors.red.shade200),
                        ),
                        child: Row(
                          children: [
                            Icon(Icons.error_outline,
                                color: Colors.red.shade600),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                _minChargeError!,
                                style: TextStyle(
                                  color: Colors.red.shade700,
                                  fontFamily: 'Gilroy',
                                ),
                              ),
                            ),
                            TextButton(
                              onPressed: _loadMinChargeInfo,
                              child: const Text('إعادة المحاولة'),
                            ),
                          ],
                        ),
                      ),
                    ),

                  // Coupon Code
                  // const CouponCodeField(),

                  // Cart Totals
                  CartTotalsCard(
                    subtotal: _totalPrice,
                    total: _totalPrice,
                    itemCount:
                        _totalQuantity, // Total quantity for order summary
                  ),

                  // Checkout Button
                  Padding(
                    padding: const EdgeInsets.all(AppDefaults.padding),
                    child: SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _canProceedToCheckout
                            ? () {
                                nav.Router.push(context, const CheckoutPage());
                              }
                            : () {
                                _showMinChargeError();
                              },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: _canProceedToCheckout
                              ? Theme.of(context).primaryColor
                              : Colors.grey,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                        ),
                        child: Text(
                          _canProceedToCheckout
                              ? 'متابعة للدفع'
                              : 'لا يمكن الدفع - لم تصل للحد الأدنى',
                          style: const TextStyle(
                            fontFamily: 'Gilroy',
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildEmptyCart() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppDefaults.padding * 2),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.shopping_cart_outlined,
              size: 80,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 24),
            Text(
              'سلة التسوق فارغة',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontFamily: 'Gilroy',
                    fontWeight: FontWeight.bold,
                    color: Colors.grey.shade600,
                  ),
            ),
            const SizedBox(height: 12),
            Text(
              'أضف منتجات إلى سلة التسوق للمتابعة',
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    fontFamily: 'Gilroy',
                    color: Colors.grey.shade500,
                  ),
            ),
            const SizedBox(height: 32),
            ElevatedButton(
              onPressed: () {
                final entryPointState =
                    context.findAncestorStateOfType<EntryPointUIState>();
                entryPointState?.onBottomNavigationTap(0);
              },
              child: const Text(
                'العودة للتسوق',
                style: TextStyle(
                  fontFamily: 'Gilroy',
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
