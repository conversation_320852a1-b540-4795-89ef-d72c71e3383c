import 'package:flutter/material.dart';

import '../../../../utils/navigation.dart' as nav;
import '../../../../views/profile/order/order_details.dart';
import '../../../../api/orders_api.dart';
import 'order_preview_tile.dart';

class CompletedTab extends StatelessWidget {
  const CompletedTab({
    super.key,
    required this.orders,
  });

  final List<OrderResponse> orders;

  @override
  Widget build(BuildContext context) {
    if (orders.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.check_circle_outline,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              'لا توجد طلبات سابقة',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.only(top: 8),
      itemCount: orders.length,
      itemBuilder: (context, index) {
        final order = orders[index];
        return OrderPreviewTile(
          order: order,
          onTap: () => nav.Router.push(
            context,
            OrderDetailsPage(
              orderId: order.id,
              storeId: order.storeId,
            ),
          ),
        );
      },
    );
  }
}
