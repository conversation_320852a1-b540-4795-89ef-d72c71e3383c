import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/constants/app_defaults.dart';

import '../../../core/components/title_and_action_button.dart';
import '../../../api/wholesaler_api.dart';
import '../../../models/cart_models.dart';
import '../../../services/multi_cart_service.dart';
import '../../../services/app_services.dart';
import '../../../utils/navigation.dart' as nav;
import '../wholesaler_products_page.dart';
import '../all_wholesalers_page.dart';

class WholesalerGrid extends StatefulWidget {
  const WholesalerGrid({super.key});

  @override
  State<WholesalerGrid> createState() => _WholesalerGridState();
}

class _WholesalerGridState extends State<WholesalerGrid> {
  List<WholesalerInfo> _wholesalers = [];
  bool _isLoading = false;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadWholesalers();

    // Listen for store changes to refresh wholesalers
    AppServices().storeService.addListener(_onStoreChanged);
  }

  @override
  void dispose() {
    AppServices().storeService.removeListener(_onStoreChanged);
    super.dispose();
  }

  void _onStoreChanged() {
    // Refresh wholesalers when store selection changes
    _loadWholesalers();
  }

  Future<void> _loadWholesalers() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // Get current region from store service
      final storeService = AppServices().storeService;
      final regionId = storeService.selectedStore?.city.id;

      final response = await WholesalerApiService.getAllWholesalers(
        pageSize: 20,
        regionId: regionId,
      );
      setState(() {
        _wholesalers = response.wholesalers;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  void _navigateToWholesaler(WholesalerInfo wholesaler) {
    nav.Router.push(
      context,
      WholesalerProductsPage(
        wholesalerId: wholesaler.id,
        isHomePage: false,
      ),
    );
  }

  void _showAllWholesalers() {
    nav.Router.push(
      context,
      const AllWholesalersPage(),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        TitleAndActionButton(
          title: 'التجار ',
          onTap: _wholesalers.length > 6 ? _showAllWholesalers : () {},
        ),
        const SizedBox(height: AppDefaults.padding),
        _buildWholesalerGrid(),
      ],
    );
  }

  Widget _buildWholesalerGrid() {
    if (_isLoading) {
      return Container(
        height: 200,
        alignment: Alignment.center,
        child: const CircularProgressIndicator(),
      );
    }

    if (_error != null) {
      return Container(
        height: 200,
        alignment: Alignment.center,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 48,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'حدث خطأ في تحميل التجار',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Text(
              _error!,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[600],
                  ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadWholesalers,
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    if (_wholesalers.isEmpty) {
      return Container(
        height: 200,
        alignment: Alignment.center,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.store_outlined,
              size: 48,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد تجار متاحون',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Text(
              'سيتم إضافة التجار قريباً',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[600],
                  ),
            ),
          ],
        ),
      );
    }

    // Show up to 6 wholesalers in a 2x3 grid
    final displayWholesalers = _wholesalers.take(6).toList();

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: AppDefaults.padding),
      child: GridView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          childAspectRatio: 1.2,
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
        ),
        itemCount: displayWholesalers.length,
        itemBuilder: (context, index) {
          final wholesaler = displayWholesalers[index];
          return _WholesalerCard(
            wholesaler: wholesaler,
            onTap: () => _navigateToWholesaler(wholesaler),
          );
        },
      ),
    );
  }
}

class _WholesalerCard extends StatelessWidget {
  final WholesalerInfo wholesaler;
  final VoidCallback onTap;

  const _WholesalerCard({
    required this.wholesaler,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<MultiCartService>(
      builder: (context, multiCartService, child) {
        final hasCart =
            multiCartService.getCartForWholesaler(wholesaler.id) != null;
        final cartItemCount =
            multiCartService.getCartForWholesaler(wholesaler.id)?.itemCount ??
                0;

        return GestureDetector(
          onTap: onTap,
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: hasCart ? AppColors.primary : Colors.grey[300]!,
                width: hasCart ? 2 : 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Stack(
              children: [
                Column(
                  children: [
                    // Logo section
                    Expanded(
                      flex: 3,
                      child: Container(
                        width: double.infinity,
                        decoration: BoxDecoration(
                          color: Colors.grey[50],
                          borderRadius: const BorderRadius.only(
                            topLeft: Radius.circular(12),
                            topRight: Radius.circular(12),
                          ),
                        ),
                        child: wholesaler.logoUrl != null
                            ? ClipRRect(
                                borderRadius: const BorderRadius.only(
                                  topLeft: Radius.circular(12),
                                  topRight: Radius.circular(12),
                                ),
                                child: Image.network(
                                  wholesaler.logoUrl!,
                                  fit: BoxFit.fitHeight,
                                  errorBuilder: (context, error, stackTrace) {
                                    return Icon(
                                      Icons.store,
                                      size: 40,
                                      color: Colors.grey[400],
                                    );
                                  },
                                  loadingBuilder:
                                      (context, child, loadingProgress) {
                                    if (loadingProgress == null) return child;
                                    return Center(
                                      child: CircularProgressIndicator(
                                        value: loadingProgress
                                                    .expectedTotalBytes !=
                                                null
                                            ? loadingProgress
                                                    .cumulativeBytesLoaded /
                                                loadingProgress
                                                    .expectedTotalBytes!
                                            : null,
                                      ),
                                    );
                                  },
                                ),
                              )
                            : Icon(
                                Icons.store,
                                size: 40,
                                color: Colors.grey[400],
                              ),
                      ),
                    ),
                    // Title section
                    Expanded(
                      flex: 2,
                      child: Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(8),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              wholesaler.title,
                              style: Theme.of(context)
                                  .textTheme
                                  .titleSmall
                                  ?.copyWith(
                                    fontWeight: FontWeight.bold,
                                    color: hasCart
                                        ? AppColors.primary
                                        : Colors.black,
                                  ),
                              textAlign: TextAlign.center,
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                            // if (wholesaler.username.isNotEmpty) ...[
                            //   const SizedBox(height: 2),
                            //   Text(
                            //     '@${wholesaler.username}',
                            //     style: Theme.of(context)
                            //         .textTheme
                            //         .bodySmall
                            //         ?.copyWith(
                            //           color: Colors.grey[600],
                            //         ),
                            //     textAlign: TextAlign.center,
                            //     maxLines: 1,
                            //     overflow: TextOverflow.ellipsis,
                            //   ),
                            // ],
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
                // Cart indicator
                if (hasCart)
                  Positioned(
                    top: 8,
                    right: 8,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: AppColors.primary,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        cartItemCount.toString(),
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          ),
        );
      },
    );
  }
}
