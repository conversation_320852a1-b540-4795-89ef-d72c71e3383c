import 'package:flutter/material.dart';
import '../../../core/constants/app_defaults.dart';

enum SortOption {
  none,
  inventoryAsc,
  inventoryDesc,
  priceAsc,
  priceDesc,
  nameAsc,
  nameDesc,
}

extension SortOptionExtension on SortOption {
  String get displayName {
    switch (this) {
      case SortOption.none:
        return 'بدون ترتيب';
      case SortOption.inventoryAsc:
        return 'المخزون (أقل أولاً)';
      case SortOption.inventoryDesc:
        return 'المخزون (أكثر أولاً)';
      case SortOption.priceAsc:
        return 'السعر (أقل أولاً)';
      case SortOption.priceDesc:
        return 'السعر (أكثر أولاً)';
      case SortOption.nameAsc:
        return 'الاسم (أ-ي)';
      case SortOption.nameDesc:
        return 'الاسم (ي-أ)';
    }
  }

  IconData get icon {
    switch (this) {
      case SortOption.none:
        return Icons.sort;
      case SortOption.inventoryAsc:
        return Icons.inventory_2_outlined;
      case SortOption.inventoryDesc:
        return Icons.inventory_2;
      case SortOption.priceAsc:
        return Icons.arrow_upward;
      case SortOption.priceDesc:
        return Icons.arrow_downward;
      case SortOption.nameAsc:
        return Icons.sort_by_alpha;
      case SortOption.nameDesc:
        return Icons.sort_by_alpha;
    }
  }
}

class ProductSortControls extends StatelessWidget {
  final SortOption selectedSort;
  final Function(SortOption) onSortChanged;

  const ProductSortControls({
    super.key,
    required this.selectedSort,
    required this.onSortChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: AppDefaults.padding),
      child: Row(
        children: [
          Text(
            'ترتيب حسب:',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Container(
              height: 40,
              padding: const EdgeInsets.symmetric(horizontal: 12),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: BorderRadius.circular(8),
                color: Colors.white,
              ),
              child: DropdownButtonHideUnderline(
                child: DropdownButton<SortOption>(
                  value: selectedSort,
                  isExpanded: true,
                  icon: const Icon(Icons.keyboard_arrow_down),
                  style: Theme.of(context).textTheme.bodyMedium,
                  items: SortOption.values.map((SortOption option) {
                    return DropdownMenuItem<SortOption>(
                      value: option,
                      child: Row(
                        children: [
                          Icon(
                            option.icon,
                            size: 18,
                            color: Colors.grey.shade600,
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              option.displayName,
                              style: Theme.of(context).textTheme.bodyMedium,
                            ),
                          ),
                        ],
                      ),
                    );
                  }).toList(),
                  onChanged: (SortOption? newValue) {
                    if (newValue != null) {
                      onSortChanged(newValue);
                    }
                  },
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
