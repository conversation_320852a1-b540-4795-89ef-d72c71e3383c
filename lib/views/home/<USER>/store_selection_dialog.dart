import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/constants/app_defaults.dart';
import '../../../services/store_service.dart';
import '../../../models/store_models.dart';
import '../../../utils/navigation.dart' as nav;
import '../../../views/profile/stores/store_form_page.dart';

class StoreSelectionDialog extends StatefulWidget {
  const StoreSelectionDialog({super.key});

  @override
  State<StoreSelectionDialog> createState() => _StoreSelectionDialogState();
}

class _StoreSelectionDialogState extends State<StoreSelectionDialog> {
  late StoreService _storeService;
  String _searchQuery = '';
  List<StoreData> _filteredStores = [];

  @override
  void initState() {
    super.initState();
    _storeService = context.read<StoreService>();
    _loadStores();
  }

  Future<void> _loadStores() async {
    await _storeService.fetchStores();
    _updateFilteredStores();
  }

  void _updateFilteredStores() {
    setState(() {
      _filteredStores = _storeService.searchStores(_searchQuery);
    });
  }

  void _onSearchChanged(String query) {
    _searchQuery = query;
    _updateFilteredStores();
  }

  Future<void> _selectStore(StoreData store) async {
    try {
      await _storeService.selectStore(store);
      if (mounted) {
        Navigator.of(context).pop(true);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء اختيار المتجر: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _addNewStore() async {
    final result = await nav.Router.pushForResult(
      context,
      const StoreFormPage(),
    );
    
    if (result == true) {
      // Refresh stores list after adding new store
      await _storeService.refreshStores();
      _updateFilteredStores();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.7,
        padding: const EdgeInsets.all(AppDefaults.padding),
        child: Column(
          children: [
            // Header
            Row(
              children: [
                Expanded(
                  child: Text(
                    'اختر المتجر',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(false),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            const SizedBox(height: AppDefaults.padding),

            // Search field
            TextField(
              onChanged: _onSearchChanged,
              decoration: InputDecoration(
                hintText: 'البحث في المتاجر...',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                filled: true,
                fillColor: Colors.grey[50],
              ),
            ),
            const SizedBox(height: AppDefaults.padding),

            // Add new store button
            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: _addNewStore,
                icon: const Icon(Icons.add),
                label: const Text('إضافة متجر جديد'),
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
            const SizedBox(height: AppDefaults.padding),

            // Stores list
            Expanded(
              child: Consumer<StoreService>(
                builder: (context, storeService, child) {
                  if (storeService.isLoading) {
                    return const Center(child: CircularProgressIndicator());
                  }

                  if (storeService.error != null) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.error_outline,
                            size: 48,
                            color: Colors.grey[400],
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'حدث خطأ في تحميل المتاجر',
                            style: Theme.of(context).textTheme.titleMedium,
                          ),
                          const SizedBox(height: 8),
                          Text(
                            storeService.error!,
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                  color: Colors.grey[600],
                                ),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 16),
                          ElevatedButton(
                            onPressed: _loadStores,
                            child: const Text('إعادة المحاولة'),
                          ),
                        ],
                      ),
                    );
                  }

                  if (_filteredStores.isEmpty) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.store_outlined,
                            size: 48,
                            color: Colors.grey[400],
                          ),
                          const SizedBox(height: 16),
                          Text(
                            _searchQuery.isEmpty
                                ? 'لا توجد متاجر'
                                : 'لا توجد نتائج للبحث',
                            style: Theme.of(context).textTheme.titleMedium,
                          ),
                          const SizedBox(height: 8),
                          Text(
                            _searchQuery.isEmpty
                                ? 'قم بإضافة متجر جديد للبدء'
                                : 'جرب كلمات بحث أخرى',
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                  color: Colors.grey[600],
                                ),
                          ),
                        ],
                      ),
                    );
                  }

                  return ListView.separated(
                    itemCount: _filteredStores.length,
                    separatorBuilder: (context, index) => const Divider(),
                    itemBuilder: (context, index) {
                      final store = _filteredStores[index];
                      final isSelected = storeService.selectedStore?.id == store.id;

                      return ListTile(
                        leading: Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: isSelected
                                ? AppColors.primary.withValues(alpha: 0.2)
                                : Colors.grey[100],
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Icon(
                            Icons.store,
                            color: isSelected ? AppColors.primary : Colors.grey[600],
                            size: 20,
                          ),
                        ),
                        title: Text(
                          store.name,
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                              ),
                        ),
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              store.address,
                              style: Theme.of(context).textTheme.bodySmall,
                            ),
                            Text(
                              '${store.city.name}, ${store.state.name}',
                              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                    color: Colors.grey[600],
                                  ),
                            ),
                          ],
                        ),
                        trailing: isSelected
                            ? const Icon(
                                Icons.check_circle,
                                color: AppColors.primary,
                              )
                            : null,
                        onTap: () => _selectStore(store),
                      );
                    },
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
