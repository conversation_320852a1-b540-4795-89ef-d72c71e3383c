import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:provider/provider.dart';

import 'core/themes/app_themes.dart';
import 'services/app_services.dart';
import 'services/cart_service.dart';
import 'services/region_service.dart';
import 'services/store_service.dart';
import 'views/splash/splash_screen.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize global services
  await AppServices().initialize();

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider<CartService>.value(
          value: AppServices().cartService,
        ),
        ChangeNotifierProvider<RegionService>.value(
          value: AppServices().regionService,
        ),
        ChangeNotifierProvider<StoreService>.value(
          value: AppServices().storeService,
        ),
        ChangeNotifierProvider.value(
          value: AppServices().multiCartService,
        ),
      ],
      child: MaterialApp(
        title: 'Tager Plus',
        theme: AppTheme.defaultTheme,
        home: const SplashScreen(),
        locale: const Locale('ar', 'EG'),
        debugShowCheckedModeBanner: false,
        supportedLocales: const [
          Locale('ar', 'EG'),
        ],
        localizationsDelegates: const [
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
        ],
        localeResolutionCallback: (locale, supportedLocales) {
          return const Locale('ar', 'EG');
        },
      ),
    );
  }
}
