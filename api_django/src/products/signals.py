"""
Signal handlers for the products app.

This module contains signal handlers that are triggered on various product-related events.
"""

import logging
from django.db.models.signals import post_save
from django.dispatch import receiver
from django.conf import settings
import openai

from .models import Product
from .utils import generate_search_vector, generate_embedding

# Configure logger
logger = logging.getLogger(__name__)

# Configure OpenAI API
openai.api_key = settings.OPENAI_API_KEY


# generate embedding for product on update
@receiver(post_save, sender=Product)
def generate_embedding_on_update(sender, instance, **kwargs):
    instance.semantic_vector = generate_embedding(
        "اسم المنتج: "
        + instance.name
        + " اسم الشركة: "
        + instance.company.name
        + " اسم الفئة: "
        + instance.category.name
        + " اسم المنتج: "
        + instance.title
        + " اوصاف المنتج: "
        + instance.description
    )
    instance = generate_search_vector(instance)
    instance.save()
