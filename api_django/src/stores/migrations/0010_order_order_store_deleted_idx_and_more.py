# Generated by Django 5.2.1 on 2025-07-25 12:23

from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('products', '0006_category_category_deleted_at_idx_and_more'),
        ('stores', '0009_activeorder'),
        ('wholesalers', '0011_remove_regionmincharge_wholesalers_region__d345a5_idx_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddIndex(
            model_name='order',
            index=models.Index(fields=['store', 'deleted_at'], name='order_store_deleted_idx'),
        ),
        migrations.AddIndex(
            model_name='order',
            index=models.Index(fields=['wholesaler', 'deleted_at'], name='order_wholesaler_deleted_idx'),
        ),
        migrations.AddIndex(
            model_name='order',
            index=models.Index(fields=['status', 'deleted_at'], name='order_status_deleted_idx'),
        ),
        migrations.AddIndex(
            model_name='order',
            index=models.Index(fields=['created_at'], name='order_created_at_idx'),
        ),
        migrations.AddIndex(
            model_name='order',
            index=models.Index(fields=['wholesaler', 'status', 'deleted_at'], name='order_whlslr_status_del_idx'),
        ),
        migrations.AddIndex(
            model_name='order',
            index=models.Index(fields=['store', 'status', 'deleted_at'], name='order_store_status_deleted_idx'),
        ),
        migrations.AddIndex(
            model_name='orderitem',
            index=models.Index(fields=['order'], name='orderitem_order_idx'),
        ),
        migrations.AddIndex(
            model_name='orderitem',
            index=models.Index(fields=['product_item'], name='orderitem_product_item_idx'),
        ),
        migrations.AddIndex(
            model_name='orderitem',
            index=models.Index(fields=['product_item', 'order'], name='orderitem_product_order_idx'),
        ),
        migrations.AddIndex(
            model_name='store',
            index=models.Index(fields=['owner', 'deleted_at'], name='store_owner_deleted_idx'),
        ),
        migrations.AddIndex(
            model_name='store',
            index=models.Index(fields=['city'], name='store_city_idx'),
        ),
        migrations.AddIndex(
            model_name='store',
            index=models.Index(fields=['state'], name='store_state_idx'),
        ),
        migrations.AddIndex(
            model_name='store',
            index=models.Index(fields=['country'], name='store_country_idx'),
        ),
    ]
