{% extends 'wholesalers/base.html' %}

{% block title %}لوحة التحكم الرئيسية{% endblock %}

{% block extra_css %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h2 mb-0">
                <i class="fas fa-tachometer-alt me-2 text-success"></i>
                مرحباً {{ wholesaler.title }}
            </h1>
            <div class="d-flex gap-2">
                <button class="btn btn-primary" onclick="location.href='{% url 'item_create_step1' %}'">
                    <i class="fas fa-plus me-2"></i>
                    إضافة منتج جديد
                </button>
                <button class="btn btn-success" onclick="location.href='{% url 'bulk_add_items' %}'">
                    <i class="fas fa-upload me-2"></i>
                    إضافة مجمعة
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card stats-card orders">
            <div class="card-body">
                <div class="icon">
                    <i class="fas fa-shopping-cart"></i>
                </div>
                <h3 class="mb-1">{{ total_orders }}</h3>
                <p class="text-muted mb-0">إجمالي الطلبات</p>
                {% if pending_orders > 0 %}
                <small class="text-warning">
                    <i class="fas fa-clock me-1"></i>
                    {{ pending_orders }} طلب في الانتظار
                </small>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card stats-card items">
            <div class="card-body">
                <div class="icon">
                    <i class="fas fa-boxes"></i>
                </div>
                <h3 class="mb-1">{{ total_items }}</h3>
                <p class="text-muted mb-0">إجمالي المنتجات</p>
                {% if low_stock_items > 0 %}
                <small class="text-danger">
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    {{ low_stock_items }} منتج ينفد
                </small>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card stats-card sales">
            <div class="card-body">
                <div class="icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <h3 class="mb-1" id="totalSales">0 ج.م</h3>
                <p class="text-muted mb-0">مبيعات آخر 7 أيام</p>
                <small class="text-success">
                    <i class="fas fa-arrow-up me-1"></i>
                    نمو مستمر
                </small>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card stats-card alerts">
            <div class="card-body">
                <div class="icon">
                    <i class="fas fa-bell"></i>
                </div>
                <h3 class="mb-1">{{ pending_orders|add:low_stock_items }}</h3>
                <p class="text-muted mb-0">التنبيهات</p>
                <small class="text-info">
                    <i class="fas fa-info-circle me-1"></i>
                    تحتاج متابعة
                </small>
            </div>
        </div>
    </div>
</div>

<!-- Charts and Recent Activity -->
<div class="row">
    <!-- Sales Chart -->
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-area me-2"></i>
                    مبيعات آخر 7 أيام
                </h5>
            </div>
            <div class="card-body">
                <canvas id="salesChart" height="100"></canvas>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button class="btn btn-outline-primary" onclick="location.href='{% url 'item_create_step1' %}'">
                        <i class="fas fa-plus me-2"></i>
                        إضافة منتج جديد
                    </button>
                    <button class="btn btn-outline-success" onclick="location.href='{% url 'orders_list' %}'">
                        <i class="fas fa-eye me-2"></i>
                        عرض جميع الطلبات
                    </button>
                    <button class="btn btn-outline-info" onclick="location.href='{% url 'items_list' %}'">
                        <i class="fas fa-warehouse me-2"></i>
                        إدارة المخزون
                    </button>
                    <button class="btn btn-outline-warning" onclick="location.href='#'">
                        <i class="fas fa-chart-bar me-2"></i>
                        تقارير المبيعات
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Orders -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-clock me-2"></i>
                    الطلبات الأخيرة
                </h5>
                <a href="{% url 'orders_list' %}" class="btn btn-sm btn-outline-primary">
                    عرض الكل
                    <i class="fas fa-arrow-left ms-1"></i>
                </a>
            </div>
            <div class="card-body">
                {% if recent_orders %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>رقم الطلب</th>
                                <th>المتجر</th>
                                <th>المبلغ الإجمالي</th>
                                <th>الحالة</th>
                                <th>التاريخ</th>
                                {% comment %} <th>الإجراءات</th> {% endcomment %}
                            </tr>
                        </thead>
                        <tbody>
                            {% for order in recent_orders %}
                            <tr>
                                <td>
                                    <strong>#{{ order.id }}</strong>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-sm bg-light rounded-circle d-flex align-items-center justify-content-center me-2">
                                            <i class="fas fa-store text-muted"></i>
                                        </div>
                                        <div>
                                            <div class="fw-semibold">{{ order.store.name }}</div>
                                            <small class="text-muted">{{ order.store.owner.username }}</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="fw-bold text-success">{{ order.total_price }} ج.م</span>
                                </td>
                                <td>
                                    {% if order.status == 'pending' %}
                                        <span class="badge bg-warning">في الانتظار</span>
                                    {% elif order.status == 'processing' %}
                                        <span class="badge bg-info">قيد المعالجة</span>
                                    {% elif order.status == 'shipped' %}
                                        <span class="badge bg-primary">تم الشحن</span>
                                    {% elif order.status == 'delivered' %}
                                        <span class="badge bg-success">تم التسليم</span>
                                    {% elif order.status == 'cancelled' %}
                                        <span class="badge bg-danger">ملغي</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="text-muted">{{ order.created_at|timesince }} مضت</span>
                                </td>
                                {% comment %} <td>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-primary" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        {% if order.status == 'pending' %}
                                        <button class="btn btn-outline-success" title="قبول الطلب">
                                            <i class="fas fa-check"></i>
                                        </button>
                                        <button class="btn btn-outline-danger" title="رفض الطلب">
                                            <i class="fas fa-times"></i>
                                        </button>
                                        {% endif %}
                                    </div>
                                </td> {% endcomment %}
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد طلبات حتى الآن</h5>
                    <p class="text-muted">ستظهر الطلبات الجديدة هنا عند وصولها</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Sales data from Django
    const salesData = {{ daily_sales|safe }};
    
    // Calculate total sales
    const totalSales = salesData.reduce((sum, day) => sum + day.sales, 0);
    document.getElementById('totalSales').textContent = totalSales.toLocaleString('ar-EG') + ' ج.م';
    
    // Create sales chart
    const ctx = document.getElementById('salesChart').getContext('2d');
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: salesData.map(day => {
                const date = new Date(day.date);
                return date.toLocaleDateString('ar-EG', { weekday: 'short', month: 'short', day: 'numeric' });
            }),
            datasets: [{
                label: 'المبيعات (ج.م)',
                data: salesData.map(day => day.sales),
                borderColor: '#28a745',
                backgroundColor: 'rgba(40, 167, 69, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4,
                pointBackgroundColor: '#28a745',
                pointBorderColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: 6,
                pointHoverRadius: 8
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return value.toLocaleString('ar-EG') + ' ج.م';
                        }
                    },
                    grid: {
                        color: 'rgba(0, 0, 0, 0.1)'
                    }
                },
                x: {
                    grid: {
                        display: false
                    }
                }
            },
            elements: {
                point: {
                    hoverBackgroundColor: '#1e7e34'
                }
            }
        }
    });
});
</script>
{% endblock %}
