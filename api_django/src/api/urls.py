from ninja import Router
from typing import List

from api.home import get_products, PaginatedProductResponse
from .auth import (
    LoginResponse,
    RegisterResponse,
    UserResponse,
    login,
    register,
    get_current_user,
    update_current_user,
)
from .middleware import AuthMiddleware
from .products import get_product_by_id, ProductResponse
from .regions import (
    get_countries,
    get_states,
    get_districts,
    list_regions,
    RegionWithHierarchyOut,
)
from .wholesaler import (
    get_wholesalers,
    get_wholesaler_by_id,
    get_wholesaler_min_charge,
    get_wholesaler_items,
    get_wholesaler_item_by_id,
    WholesalerOut,
    WholesalerMinChargeOut,
    WholesalerItemOut,
    PaginatedWholesalerResponse,
)
from .stores import (
    get_user_stores,
    get_store_by_id,
    create_store,
    update_store,
    delete_store,
    StoreOut,
)
from .orders import (
    create_order,
    get_my_orders,
    get_order_by_id,
    cancel_order,
    update_order_status,
    OrderOut,
    PaginatedOrderResponse,
)
from .categories_companies import list_categories_companies, CategoryCompanyOut

# /api/v2/
router = Router(tags=["v2"])


@router.get("/")
def test(request):
    return {"message": "Hello, World!"}


# Auth
router.post("/login", response=LoginResponse, tags=["auth"])(login)
router.post("/register", response=RegisterResponse, tags=["auth"])(register)
router.get("/me", response=UserResponse, tags=["auth"], auth=AuthMiddleware)(
    get_current_user
)
router.put("/me", response=UserResponse, tags=["auth"], auth=AuthMiddleware)(
    update_current_user
)

home_router = Router(tags=["home"], auth=AuthMiddleware)
router.add_router("/home/", home_router)
home_router.get("/products", response=PaginatedProductResponse)(get_products)

products_router = Router(tags=["products"], auth=AuthMiddleware)
router.add_router("/products/", products_router)
products_router.get("/{product_id}", response=ProductResponse)(get_product_by_id)

regions_router = Router(tags=["regions"], auth=AuthMiddleware)
router.add_router("/regions/", regions_router)
regions_router.get("/", response=List[RegionWithHierarchyOut])(list_regions)
regions_router.get("/countries", response=List[RegionWithHierarchyOut])(get_countries)
regions_router.get("/states", response=List[RegionWithHierarchyOut])(get_states)
regions_router.get("/districts", response=List[RegionWithHierarchyOut])(get_districts)

wholesalers_router = Router(tags=["wholesalers"], auth=AuthMiddleware)
router.add_router("/wholesalers/", wholesalers_router)
wholesalers_router.get("/", response=PaginatedWholesalerResponse)(get_wholesalers)
wholesalers_router.get("/{wholesaler_id}", response=WholesalerOut)(get_wholesaler_by_id)
wholesalers_router.get(
    "/{wholesaler_id}/min-charge/{region_id}", response=WholesalerMinChargeOut
)(get_wholesaler_min_charge)
wholesalers_router.get("/{wholesaler_id}/items", response=List[WholesalerItemOut])(
    get_wholesaler_items
)
wholesalers_router.get("/items/{wholesaler_item_id}", response=WholesalerItemOut)(
    get_wholesaler_item_by_id
)

stores_router = Router(tags=["stores"], auth=AuthMiddleware)
router.add_router("/stores/", stores_router)
stores_router.get("/me", response=List[StoreOut])(get_user_stores)
stores_router.get("/{store_id}", response=StoreOut)(get_store_by_id)
stores_router.post("/", response=StoreOut)(create_store)
stores_router.put("/{store_id}", response=StoreOut)(update_store)
stores_router.delete("/{store_id}")(delete_store)

orders_router = Router(tags=["orders"], auth=AuthMiddleware)
router.add_router("/orders/", orders_router)
orders_router.post("/", response=OrderOut)(create_order)
orders_router.get("/my", response=PaginatedOrderResponse)(get_my_orders)
orders_router.get("/{order_id}", response=OrderOut)(get_order_by_id)
orders_router.get("/stores/{store_id}/{order_id}", response=OrderOut)(get_order_by_id)
orders_router.delete("/stores/{store_id}/{order_id}")(cancel_order)
orders_router.post("/stores/{store_id}/{order_id}/status", response=OrderOut)(
    update_order_status
)

# Wholesaler Orders Router
from .wholesaler_orders import (
    list_my_wholesaler_orders,
    accept_order,
    reject_order,
    cancel_order as cancel_wholesaler_order,
    complete_order,
    WholesalerOrdersListOut,
)

wholesaler_orders_router = Router(tags=["wholesaler-orders"], auth=AuthMiddleware)
router.add_router("/wholesaler-orders/", wholesaler_orders_router)
wholesaler_orders_router.get("/", response=WholesalerOrdersListOut)(
    list_my_wholesaler_orders
)
wholesaler_orders_router.post("/accept/{order_id}")(accept_order)
wholesaler_orders_router.post("/reject/{order_id}")(reject_order)
wholesaler_orders_router.post("/cancel/{order_id}")(cancel_wholesaler_order)
wholesaler_orders_router.post("/complete")(complete_order)

categories_companies_router = Router(tags=["categories-companies"], auth=AuthMiddleware)
router.add_router("/categories-companies", categories_companies_router)
categories_companies_router.get("/", response=CategoryCompanyOut)(
    list_categories_companies
)
